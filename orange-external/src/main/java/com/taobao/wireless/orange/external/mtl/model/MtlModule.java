package com.taobao.wireless.orange.console.manager.support.mtl.model;

import lombok.Data;

import java.util.List;

@Data
public class MtlModule {
    /**
     * 模块名称
     */
    private String name;

    /**
     * 模块ID
     */
    private Long moduleId;

    /**
     * 模块标识
     */
    private String identifier;

    /**
     * 模块描述
     */
    private String description;

    /**
     * 模块类型
     */
    private String moduleType;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 归属责任人
     */
    private String owner;

    /**
     * 工程标准化
     */
    private Boolean hasStandard;

    /**
     * 是否集成到淘宝
     */
    private Boolean hasIntegrateToTB;

    /**
     * 是否动态化
     */
    private Boolean hasDynamic;

    /**
     * 数据时间
     */
    private String ds;

    /**
     * 模块分层
     */
    private List<String> tags;

    /**
     * 代码仓库地址
     */
    private String codeLibraryAddress;

    /**
     * 单测是否通过
     */
    private Boolean hasLatestUtRecordPass;

    /**
     * 最新标准化版本
     */
    private String latestStandardVersion;

    /**
     * 最新正式版本
     */
    private String latestVersion;

    /**
     * 是否有单测记录
     */
    private Boolean hasUtRecord;

    /**
     * 最新正式版本commit
     */
    private String latestVersionCommitNumber;

    /**
     * 集成分支名
     */
    private String integrationBranchName;

    /**
     * 活跃度
     */
    private String activeLevel;

    /**
     * 包名
     */
    private String bundleName;

    /**
     * DepKey
     */
    private String depKey;

    /**
     * 管理员
     */
    private List<String> admins;

    /**
     * 开发人员
     */
    private List<String> developers;

    /**
     * 测试人员
     */
    private List<String> testers;

    /**
     * MTL地址
     */
    private String mtlAddress;
}

