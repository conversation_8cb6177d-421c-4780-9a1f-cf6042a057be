<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taobao.wireless</groupId>
        <artifactId>orange</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>orange-external</artifactId>
    <name>orange-external</name>
    <dependencies>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>orange-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-metaq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ihr</groupId>
            <artifactId>amdplatform-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-diamond-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-notify-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tair-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.mtl</groupId>
            <artifactId>mtl-open-sdk-consumer</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.normandy.credential</groupId>
            <artifactId>normandy-credential-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
