package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.annotation.OperationLog;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.model.*;
import com.taobao.wireless.orange.manager.util.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_RELEASE;

@Service
public class ReleaseOrderManager {
    @Autowired
    private ParameterManager parameterManager;
    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private ONamespaceVersionContentDAO namespaceVersionContentDAO;

    @Autowired
    private ConfigManager configManager;

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;
    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;
    @Autowired
    private OParameterDAO parameterDAO;
    @Autowired
    private OConditionDAO conditionDAO;
    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;


    public Page<OReleaseOrderDO> query(OReleaseOrderDO query, Integer page, Integer size) {
        return releaseOrderDAO.lambdaQuery()
                .eq(query.getAppKey() != null, OReleaseOrderDO::getAppKey, query.getAppKey())
                .eq(query.getNamespaceId() != null, OReleaseOrderDO::getNamespaceId, query.getNamespaceId())
                .eq(query.getReleaseVersion() != null, OReleaseOrderDO::getReleaseVersion, query.getReleaseVersion())
                .eq(query.getStatus() != null, OReleaseOrderDO::getStatus, query.getStatus())
                .orderByDesc(OReleaseOrderDO::getId)
                .page(PageUtil.build(page, size));
    }

    /**
     * 新建发布
     *
     * @param releaseOrderBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.CREATE)
    public String create(ReleaseOrderBO releaseOrderBO) {
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setAppKey(namespace.getAppKey());
        // todo: 获取 namespace 锁

        // 检测涉及发布实体是否有在变更中
        checkReleaseObjectIsPublishing(releaseOrderBO);

        // 生成发布单
        String releaseVersion = createReleaseOrder(releaseOrderBO);

        // 新增条件版本记录
        conditionManager.createConditionVersions(namespace, releaseVersion, releaseOrderBO.getConditionVersionBOS());

        // 新增参数版本记录
        parameterManager.createParameterVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersionBOS());

        // 新增参数条件版本记录
        parameterManager.createParameterConditionVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersionBOS(), releaseOrderBO.getConditionVersionBOS());

        // 新增 namespace 版本记录
        namespaceVersionManager.upgradeChangeVersion(releaseOrderBO.getNamespaceId(), releaseVersion, NamespaceVersionChangeType.NEW_RELEASE);

        // todo: 释放 namespace 锁
        return releaseVersion;
    }

    /**
     * 正式发布
     *
     * @param releaseVersion
     */
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.RELEASE)
    public void publish(String releaseVersion) {
        var releaseOrder = getReleaseOrderByReleaseVersion(releaseVersion);
        // fixme: 为了测试去掉先
//        if (!ReleaseOrderStatus.VERIFY_PASS.equals(releaseOrder.getStatus())) {
//            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_NOT_VERIFY_PASS);
//        }

        // todo: 获取 namespace 锁
        onlineParameterVersion(releaseVersion);

        onlineConditionVersion(releaseVersion);

        onlineParameterConditionVersion(releaseVersion);

        // namespace 版本号递增
        var namespaceVersion = namespaceVersionManager.upgradeVersion(releaseOrder.getNamespaceId(), releaseVersion, FINISH_RELEASE);

        // 将正式版本内容保存（用于回滚）
        saveNamespaceVersionContent(releaseOrder, namespaceVersion);

        // 更新发布单状态
        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .set(OReleaseOrderDO::getStatus, ReleaseOrderStatus.RELEASED)
                .update();

        // todo: 释放 namespace 锁
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.CANCEL)
    public void cancel(String releaseVersion) {
        var releaseOrder = getReleaseOrderByReleaseVersion(releaseVersion);

        if (releaseOrder.getStatus().isFinished()) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }

        // todo: 获取 namespace 锁
        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.CANCELED).update();

        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.CANCELED).update();

        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.CANCELED).update();

        namespaceVersionManager.upgradeChangeVersion(releaseOrder.getNamespaceId(), releaseVersion, NamespaceVersionChangeType.CANCEL_RELEASE);

        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .set(OReleaseOrderDO::getStatus, ReleaseOrderStatus.CANCELED)
                .update();
        // todo: 释放 namespace 锁
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.VERIFY)
    public void verify(String releaseVersion) {
        // todo: 获取 namespace 锁
        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .set(OReleaseOrderDO::getStatus, ReleaseOrderStatus.VERIFY_PASS)
                .update();

        // todo: 释放 namespace 锁
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLog(type = OperationType.RATIO_GRAY)
    public void ratioGray(String releaseVersion, int percent) {
        // todo: 获取 namespace 锁
        var releaseOrder = getReleaseOrderByReleaseVersion(releaseVersion);
        // todo: 状态检查
        if (releaseOrder.getPercent() != null && releaseOrder.getPercent() > percent) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "灰度比例不能小于已有的灰度比例");
        }

        namespaceVersionManager.upgradeChangeVersion(releaseOrder.getNamespaceId(), releaseVersion, NamespaceVersionChangeType.RATIO_GRAY);

        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .set(OReleaseOrderDO::getPercent, percent)
                .set(OReleaseOrderDO::getStatus, ReleaseOrderStatus.IN_RATIO_GRAY)
                .update();
        // todo: 释放 namespace 锁
    }

    /**
     * 查询发布单变更内容
     *
     * @param releaseVersion
     * @return
     */
    public List<ParameterChangeBO> getChanges(String releaseVersion) {
        // 查询本次发布单涉及的参数
        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion).list();
        List<String> parameterIds = parameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .collect(Collectors.toList());
        Map<String, List<OParameterConditionVersionDO>> initParameterConditionVersions = parameterManager.getParameterConditionVersionsByStatus(parameterIds, VersionStatus.INIT, false);

        // 查询修改前参数信息
        var onlineParamId2Version = parameterManager.getOnlineParameterVersions(parameterIds);
        var onlineParamId2ParamCondition = parameterManager.getOnlineParameterConditionVersions(parameterIds);

        return parameters.stream().map(version -> {
            ParameterChangeBO parameterChange = new ParameterChangeBO();

            // 本次修改的内容
            ParameterVersionBO change = BeanUtil.createFromProperties(version, ParameterVersionBO.class);
            Optional.ofNullable(initParameterConditionVersions.get(version.getParameterId()))
                    .ifPresent(i -> {
                        change.setParameterConditionVersionBOS(BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class));
                    });
            parameterChange.setChange(change);

            // 修改前的参数信息（即：线上参数信息
            OParameterVersionDO oldParameterVersion = onlineParamId2Version.get(version.getParameterId());
            if (oldParameterVersion != null) {
                ParameterVersionBO before = BeanUtil.createFromProperties(oldParameterVersion, ParameterVersionBO.class);
                Optional.ofNullable(onlineParamId2ParamCondition.get(version.getParameterId()))
                        .ifPresent(i -> {
                            var conditionId2ParameterCondition = BeanUtil.createFromProperties(i, ParameterConditionVersionBO.class).stream().collect(Collectors.toMap(ParameterConditionVersionBO::getConditionId, Function.identity()));

                            List<ParameterConditionVersionBO> sortedParameterConditions = parameterManager.getConditionsOrder(before).stream().map(conditionId2ParameterCondition::get).collect(Collectors.toList());
                            sortedParameterConditions.add(conditionId2ParameterCondition.get(DEFAULT_CONDITION_ID));

                            before.setParameterConditionVersionBOS(sortedParameterConditions);
                        });
                parameterChange.setBefore(before);
            }

            return parameterChange;
        }).collect(Collectors.toList());

        // todo: 参数变更
//        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery().eq(OConditionVersionDO::getReleaseVersion, releaseVersion).list();
    }

    /**
     * 获取改动前的参数版本
     *
     * @param releaseVersion
     * @param parameterIds
     * @return
     */
    private Map<String, OParameterVersionDO> getPreviousParameters(String releaseVersion, List<String> parameterIds) {
        ONamespaceVersionDO namespaceVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .eq(ONamespaceVersionDO::getChangeType, FINISH_RELEASE)
                .one();

        // 如果当前还未发布过，则使用线上最新的版本内容
        if (namespaceVersion == null) {
            var onlineParamId2Version = parameterManager.getOnlineParameterVersions(parameterIds);
            var onlineParamId2ParamCondition = parameterManager.getOnlineParameterConditionVersions(parameterIds);
        }

        // 如果已经发布过，则从快照中去取上一个版本内容
        namespaceVersionContentDAO.lambdaQuery()
                .eq(ONamespaceVersionContentDO::getNamespaceId, namespaceVersion.getNamespaceId())
                .le(ONamespaceVersionContentDO::getNamespaceVersion, namespaceVersion.getNamespaceVersion())
                .oneOpt()
                .ifPresent(i -> {
                    try {
                        NamespaceVersionContentBO releaseConfig = JSON.parseObject(i.getContent(), NamespaceVersionContentBO.class);
                    } catch (Exception e) {
                        // todo: fixme
                    }
                });

        return null;
    }

    /**
     * 获取发布单操作记录
     *
     * @param releaseVersion
     * @return
     */
    public List<OReleaseOrderOperationDO> getOperations(String releaseVersion, List<OperationType> operationTypes) {
        return releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .in(CollectionUtils.isNotEmpty(operationTypes), OReleaseOrderOperationDO::getType, operationTypes)
                .orderByDesc(OReleaseOrderOperationDO::getId)
                .list();
    }

    /**
     * 获取发布单详情
     *
     * @param releaseVersion
     * @return
     */
    public ReleaseOrderBO getDetail(String releaseVersion) {
        OReleaseOrderDO releaseOrderDO = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        ReleaseOrderBO releaseOrder = BeanUtil.createFromProperties(releaseOrderDO, ReleaseOrderBO.class);
        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        releaseOrder.setParameterVersionBOS(BeanUtil.createFromProperties(parameters, ParameterVersionBO.class));
        releaseOrder.setConditionVersionBOS(BeanUtil.createFromProperties(conditions, ConditionVersionBO.class));
        return releaseOrder;
    }


    private void onlineParameterVersion(String releaseVersion) {
        List<OParameterVersionDO> parameters = parameterVersionDAO
                .lambdaQuery()
                .select(OParameterVersionDO::getParameterId, OParameterVersionDO::getChangeType)
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameters)) {
            return;
        }

        // 将历史发布版本标记为过期
        List<String> parameterIds = parameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        parameterVersionDAO.lambdaUpdate()
                .in(OParameterVersionDO::getParameterId, parameterIds)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OParameterVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的参数对象状态置为删除
        List<String> deleteParameterIds = parameters.stream()
                .filter(p -> ChangeType.DELETE.equals(p.getChangeType()))
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, deleteParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.DELETED)
                    .update();
        }
    }

    private void onlineConditionVersion(String releaseVersion) {
        List<OConditionVersionDO> conditions = conditionVersionDAO
                .lambdaQuery()
                .select(OConditionVersionDO::getConditionId, OConditionVersionDO::getChangeType)
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }

        // 将历史发布版本标记为过期
        List<String> conditionIds = conditions.stream()
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        conditionVersionDAO.lambdaUpdate()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OConditionVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的条件对象状态置为删除
        List<String> deleteConditionIds = conditions.stream()
                .filter(c -> ChangeType.DELETE.equals(c.getChangeType()))
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, deleteConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.DELETED)
                    .update();
        }
    }

    private void onlineParameterConditionVersion(String releaseVersion) {
        var parameterConditionVersions = parameterConditionVersionDAO.lambdaQuery()
                .select(OParameterConditionVersionDO::getParameterId, OParameterConditionVersionDO::getConditionId)
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameterConditionVersions)) {
            return;
        }

        // 将历史发布版本标记为过期
        var updateChainWrapper = parameterConditionVersionDAO.lambdaUpdate();
        for (var parameterCondition : parameterConditionVersions) {
            updateChainWrapper.or(i -> i
                    .eq(OParameterConditionVersionDO::getParameterId, parameterCondition.getParameterId())
                    .eq(OParameterConditionVersionDO::getConditionId, parameterCondition.getConditionId())
                    .eq(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED));
        }
        updateChainWrapper.set(OParameterConditionVersionDO::getStatus, VersionStatus.OUTDATED).update();

        // 将本次发布版本标记为发布
        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();
    }

    private String createReleaseOrder(ReleaseOrderBO releaseOrderBO) {
        String releaseVersion = String.valueOf(SerializeUtil.version());
        releaseOrderBO.setReleaseVersion(releaseVersion);
        releaseOrderBO.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrderBO.setBizId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setStatus(ReleaseOrderStatus.INIT);
        releaseOrderBO.setReleaseType(ReleaseType.PUBLISH);

        // 新增发布单
        boolean success = releaseOrderDAO.save(releaseOrderBO);
        if (!success) {
            throw new CommonException(ExceptionEnum.CREATE_RELEASE_ORDER_FAIL);
        }
        return releaseVersion;
    }

    /**
     * 检测发布实体是否在发布中
     *
     * @param releaseOrder
     */
    private void checkReleaseObjectIsPublishing(ReleaseOrderBO releaseOrder) {
        if (CollectionUtils.isNotEmpty(releaseOrder.getParameterVersionBOS())) {
            List<String> parameterIds = releaseOrder.getParameterVersionBOS().stream()
                    .map(ParameterVersionBO::getParameterId)
                    .collect(Collectors.toList());

            Long count = parameterVersionDAO.lambdaQuery()
                    .in(OParameterVersionDO::getParameterId, parameterIds)
                    .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                    .count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.PARAMETER_IS_PUBLISHING);
            }
        }

        if (CollectionUtils.isNotEmpty(releaseOrder.getConditionVersionBOS())) {
            List<String> conditionIds = releaseOrder.getConditionVersionBOS().stream()
                    .map(ConditionVersionBO::getConditionId)
                    .collect(Collectors.toList());

            Long count = conditionVersionDAO.lambdaQuery()
                    .in(OConditionVersionDO::getConditionId, conditionIds)
                    .eq(OConditionVersionDO::getStatus, VersionStatus.INIT)
                    .count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.CONDITION_IS_PUBLISHING);
            }
        }
    }

    private OReleaseOrderDO getReleaseOrderByReleaseVersion(String releaseVersion) {
        return releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST));
    }

    /**
     * 将正式内容保存（用于回滚）
     *
     * @param releaseOrder
     * @param namespaceVersion
     */
    private void saveNamespaceVersionContent(OReleaseOrderDO releaseOrder, String namespaceVersion) {
        ONamespaceVersionContentDO namespaceVersionContentDO = new ONamespaceVersionContentDO();
        namespaceVersionContentDO.setNamespaceId(releaseOrder.getNamespaceId());
        namespaceVersionContentDO.setNamespaceVersion(namespaceVersion);
        namespaceVersionContentDO.setAppKey(releaseOrder.getAppKey());

        NamespaceIdNamePairBO namespace = Optional.ofNullable(namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId()))
                .map(n -> NamespaceIdNamePairBO.builder()
                        .name(n.getName())
                        .namespaceId(n.getNamespaceId())
                        .build())
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
        Optional.ofNullable(fullReleaseConfigGenerator.generate(namespace, "0"))
                .ifPresent(content -> {
                    var namespaceVersionContent = new NamespaceVersionContentBO();
                    namespaceVersionContent.setParameters(content.getParameters());
                    if (CollectionUtils.isNotEmpty(content.getConditions())) {
                        List<String> conditionIds = content.getConditions()
                                .stream()
                                .map(Condition::getId)
                                .collect(Collectors.toList());
                        var conditionId2Version = conditionManager.getOnlineConditionVersions(conditionIds);
                        var conditionId2Name = conditionDAO.lambdaQuery()
                                .select(OConditionDO::getConditionId, OConditionDO::getName)
                                .in(OConditionDO::getConditionId, conditionIds)
                                .list()
                                .stream()
                                .collect(Collectors.toMap(OConditionDO::getConditionId, OConditionDO::getName));
                        var conditions = content.getConditions().stream().map(c -> {
                            var condition = new NamespaceVersionContentBO.Condition();
                            condition.setConditionId(c.getId());
                            condition.setName(conditionId2Name.get(c.getId()));
                            condition.setExpression(c.getExpression());
                            condition.setReleaseVersion(conditionId2Version.get(c.getId()).getReleaseVersion());
                            return condition;
                        }).toList();
                        namespaceVersionContent.setConditions(conditions);
                    }
                    namespaceVersionContentDO.setContent(JSON.toJSONString(namespaceVersionContent));
                });
        namespaceVersionContentDAO.save(namespaceVersionContentDO);
    }
}
