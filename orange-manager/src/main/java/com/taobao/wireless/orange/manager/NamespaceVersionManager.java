package com.taobao.wireless.orange.manager;

import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceVersionDO;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class NamespaceVersionManager {

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;
    @Autowired
    private NamespaceManager namespaceManager;

    public ONamespaceVersionDO create(String namespaceId, String appKey, String releaseVersion) {
        ONamespaceVersionDO namespaceVersionDO = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .one();
        if (namespaceVersionDO != null) {
            // 将上一个版本失效
            namespaceVersionDAO.lambdaUpdate()
                    .eq(ONamespaceVersionDO::getId, namespaceVersionDO.getId())
                    .set(ONamespaceVersionDO::getIsAvailable, Available.N)
                    .update();
        }

        // 新增版本
        ONamespaceVersionDO newNamespaceVersionDO = new ONamespaceVersionDO();
        newNamespaceVersionDO.setNamespaceId(namespaceId);
        newNamespaceVersionDO.setAppKey(appKey);
        newNamespaceVersionDO.setIsAvailable(Available.Y);
        newNamespaceVersionDO.setChangeType(NamespaceVersionChangeType.NEW_RELEASE);
        newNamespaceVersionDO.setNamespaceChangeVersion(String.valueOf(SerializeUtil.version()));
        newNamespaceVersionDO.setReleaseVersion(releaseVersion);
        if (namespaceVersionDO != null) {
            newNamespaceVersionDO.setNamespaceVersion(namespaceVersionDO.getNamespaceVersion());
        }
        namespaceVersionDAO.save(newNamespaceVersionDO);
        return newNamespaceVersionDO;
    }

    public String upgradeVersion(String namespaceId, String releaseVersion, NamespaceVersionChangeType changeType) {
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(namespaceId);

        // 失效上一个版本
        namespaceVersionDAO.lambdaUpdate()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .set(ONamespaceVersionDO::getIsAvailable, Available.N)
                .update();

        // 新增版本
        ONamespaceVersionDO newNamespaceVersion = new ONamespaceVersionDO();
        newNamespaceVersion.setAppKey(namespace.getAppKey());
        newNamespaceVersion.setNamespaceId(namespaceId);
        newNamespaceVersion.setReleaseVersion(releaseVersion);
        newNamespaceVersion.setIsAvailable(Available.Y);
        newNamespaceVersion.setChangeType(changeType);

        String newVersion = String.valueOf(SerializeUtil.version());
        newNamespaceVersion.setNamespaceVersion(newVersion);
        newNamespaceVersion.setNamespaceChangeVersion(newVersion);

        namespaceVersionDAO.save(newNamespaceVersion);
        return newVersion;
    }

    public void upgradeChangeVersion(String namespaceId, String releaseVersion, NamespaceVersionChangeType changeType) {
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(namespaceId);
        var namespaceVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .oneOpt()
                .map(ONamespaceVersionDO::getNamespaceVersion)
                .orElse(null);

        // 失效上一个版本
        namespaceVersionDAO.lambdaUpdate()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .set(ONamespaceVersionDO::getIsAvailable, Available.N)
                .update();

        // 新增版本
        ONamespaceVersionDO newNamespaceVersion = new ONamespaceVersionDO();
        newNamespaceVersion.setAppKey(namespace.getAppKey());
        newNamespaceVersion.setNamespaceId(namespaceId);
        newNamespaceVersion.setReleaseVersion(releaseVersion);
        newNamespaceVersion.setIsAvailable(Available.Y);
        newNamespaceVersion.setChangeType(changeType);
        newNamespaceVersion.setNamespaceVersion(namespaceVersion);

        String newVersion = String.valueOf(SerializeUtil.version());
        newNamespaceVersion.setNamespaceChangeVersion(newVersion);

        namespaceVersionDAO.save(newNamespaceVersion);
    }

    /**
     * 根据 changeVersionRange (左开右闭）区间查询有效的 namespaceVersion
     *
     * @param appKey
     * @param changeVersionRange
     * @return
     */
    public List<ONamespaceVersionDO> getAvailableNamespaceVersions(String appKey, Pair<String, String> changeVersionRange) {
        return namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .gt(changeVersionRange.getLeft() != null, ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getLeft())
                .le(changeVersionRange.getRight() != null, ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getRight())
                .list();
    }

    public List<ONamespaceVersionDO> getAvailableNamespaceVersions(String appKey) {
        return namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .list();
    }
}
